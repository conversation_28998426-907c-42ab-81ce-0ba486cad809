# 控件类别文字显示功能

## 功能概述

在原有的UNI控件识别红色边框高亮功能基础上，新增了控件类别文字显示功能。当鼠标悬停在控件上时，除了显示红色边框外，还会在边框旁边显示控件的类别信息，帮助用户更好地识别控件类型。

## 功能特性

### 1. 智能文字标签显示
- **位置自适应**: 文字标签优先显示在控件右上角，当接近屏幕边界时自动调整到左边或下方
- **内容智能**: 根据控件信息智能生成显示文字，优先显示控件角色类型的中文翻译
- **简洁明了**: 当控件名称较短时显示"类型:名称"，名称过长时只显示类型

### 2. 控件类型中文翻译
支持常见控件类型的中文翻译，包括：
- `push button` → 按钮
- `entry` → 输入框
- `text` → 文本
- `label` → 标签
- `menu` → 菜单
- `menu item` → 菜单项
- `combo box` → 下拉框
- `check box` → 复选框
- `radio button` → 单选按钮
- `dialog` → 对话框
- 等等...

### 3. 视觉效果
- **黑色背景**: 文字标签使用黑色背景，确保在各种界面下都有良好的对比度
- **白色文字**: 使用白色文字，保证清晰可读
- **边框协调**: 与红色高亮边框协调显示，不会相互干扰

## 技术实现

### 核心组件

#### 1. HighlightRenderer 增强
```python
class HighlightRenderer:
    def __init__(self, debug=False):
        # 原有边框窗口
        self.top_border = None
        self.bottom_border = None
        self.left_border = None
        self.right_border = None

        # 新增文字标签窗口
        self.text_label = None
        self.text_gc = None
```

#### 2. 文字标签显示方法
```python
def highlight_widget(self, x: int, y: int, width: int, height: int, widget_info: dict = None):
    """高亮显示控件并显示控件类别文字"""
    # 显示边框
    # ...

    # 显示控件类别文字标签
    self._show_widget_label(x, y, width, height, widget_info)
```

#### 3. 智能文字生成
```python
def _generate_label_text(self, widget_info: dict = None) -> str:
    """生成控件标签文字"""
    role = widget_info.get('Rolename', widget_info.get('Role', ''))
    name = widget_info.get('Name', '')

    # 角色类型翻译
    role_cn = role_translations.get(role.lower(), role)

    # 生成标签文字
    if name and len(name) <= 10:
        return f"{role_cn}:{name}"
    else:
        return role_cn
```

### 调用链路

1. **auto_recording_manager.py** 中的悬停检测触发控件识别
2. 调用 **widget_capture_module.py** 中的 `HighlightRenderer.highlight_widget()`
3. 传递完整的 `widget_info` 参数，包含控件的详细信息
4. `_show_widget_label()` 方法根据控件信息生成并显示文字标签
5. `_generate_label_text()` 方法智能生成显示文字

## 使用方法

### 1. 在GAT全流程录制中使用
启动GAT全流程录制功能后，鼠标悬停在任何控件上时会自动显示：
- 红色边框高亮
- 控件类别文字标签

### 2. 在控件捕获测试中使用
```python
from widget_capture_module import HighlightRenderer

# 创建高亮渲染器
renderer = HighlightRenderer(debug=True)

# 模拟控件信息
widget_info = {
    "Name": "登录按钮",
    "Rolename": "push button",
    "Coords": {"x": 200, "y": 200, "width": 100, "height": 40}
}

# 高亮显示控件（包含文字标签）
coords = widget_info["Coords"]
renderer.highlight_widget(
    coords["x"], coords["y"],
    coords["width"], coords["height"],
    widget_info
)
```

## 兼容性

### 环境要求
- **图形环境**: 需要X11图形环境支持
- **Python库**: 需要python3-xlib库
- **系统**: 支持Linux桌面环境

### 错误处理
- 在无图形环境中会优雅降级，不影响其他功能
- Xlib初始化失败时会跳过高亮显示，但不会报错
- 控件信息缺失时会显示默认文字或跳过显示

## 测试验证

运行测试脚本验证功能：
```bash
cd scripts
python3 test_widget_label.py
```

测试内容包括：
1. 标签文字生成功能测试
2. 不同类型控件的高亮显示测试
3. 边界情况处理测试
4. 资源清理测试

## 问题修复

### 1. 文字颜色问题修复
- **问题**: 原始实现中文字颜色设置不正确
- **修复**: 使用正确的Xlib颜色分配方法，确保白色文字在黑色背景上清晰显示
- **代码**:
```python
colormap = self.screen.default_colormap
white_color = colormap.alloc_named_color("white")
black_color = colormap.alloc_named_color("black")
self.text_gc = self.text_label.create_gc(
    foreground=white_color.pixel,
    background=black_color.pixel
)
```

### 2. 中文显示问题修复
- **问题**: 中文字符无法正确显示或出现乱码
- **修复**: 实现了多层级的文字显示策略
  1. 优先尝试UTF-8编码的中文显示
  2. 中文失败时自动切换到英文显示
  3. 最后使用ASCII转换确保基本显示
- **特性**:
  - 支持Unicode字体加载
  - 中英文自动切换
  - ASCII备用方案

### 3. 字体支持优化
- **改进**: 尝试加载支持中文的Unicode字体
- **备用方案**: 字体加载失败时使用系统默认字体
- **字体列表**:
  - Unicode字体: `-*-*-medium-r-*-*-14-*-*-*-*-*-iso10646-1`
  - 默认字体: `-*-*-medium-r-*-*-12-*-*-*-*-*-*-*`
  - 备用字体: `fixed`

## 测试验证

### 1. 功能测试
运行测试脚本验证所有功能：
```bash
cd scripts
python3 test_widget_label.py      # 基础功能测试
python3 test_text_rendering.py    # 文字渲染测试
```

### 2. 测试覆盖
- ✅ 标签文字生成功能
- ✅ 中英文自动切换
- ✅ ASCII转换功能
- ✅ 颜色设置验证
- ✅ 字体加载测试
- ✅ 多种绘制方法测试

### 3. 兼容性测试
- ✅ 无图形环境优雅降级
- ✅ Xlib初始化失败处理
- ✅ 字体加载失败处理
- ✅ 文字绘制失败备用方案

## 后续优化方向

1. **字体优化**: 支持自定义字体大小和样式
2. **颜色主题**: 支持自定义文字标签的颜色主题
3. **显示时长**: 支持配置文字标签的显示时长
4. **更多控件类型**: 扩展更多控件类型的中文翻译
5. **多语言支持**: 支持英文、中文等多语言切换

## 总结

控件类别文字显示功能是对原有控件识别功能的重要增强，通过在红色边框旁边显示控件类别信息，大大提升了用户体验和控件识别的直观性。

**主要成就**:
1. ✅ **完整实现**: 成功在红色边框旁边显示控件类别文字
2. ✅ **问题修复**: 解决了文字颜色和中文显示问题
3. ✅ **智能适配**: 实现了中英文自动切换和位置自适应
4. ✅ **稳定可靠**: 添加了完善的错误处理和备用方案
5. ✅ **完美集成**: 与现有GAT全流程录制系统无缝集成

该功能为自动化测试用例的录制提供了更好的可视化支持，让用户能够更直观地识别和理解控件类型。
