#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试文字渲染功能
专门测试中文显示和颜色问题的修复
"""

import sys
import time
import os

# 添加当前目录到Python路径
sys.path.insert(0, '.')

def test_text_rendering():
    """测试文字渲染功能"""
    print("🧪 测试文字渲染功能")
    print("=" * 50)

    try:
        from widget_capture_module import HighlightRenderer

        # 创建渲染器
        renderer = HighlightRenderer(debug=True)

        # 测试文字生成
        test_widgets = [
            {"Rolename": "push button", "Name": "确定"},
            {"Rolename": "entry", "Name": "用户名"},
            {"Rolename": "text", "Name": ""},
            {"Rolename": "menu", "Name": "文件"},
            {"Rolename": "unknown", "Name": "测试控件"},
        ]

        print("\n1. 测试文字生成功能:")
        for i, widget in enumerate(test_widgets):
            label_text = renderer._generate_label_text(widget)
            print(f"  控件{i+1}: {widget} -> '{label_text}'")

            # 检查是否生成了英文备用文字
            if hasattr(renderer, '_last_widget_info'):
                en_text = renderer._last_widget_info.get('label_text_en', '')
                print(f"    英文备用: '{en_text}'")

        print("\n2. 测试ASCII转换功能:")
        test_texts = [
            "按钮:确定",
            "输入框:用户名",
            "菜单:文件",
            "未知控件",
            "Button:OK",  # 纯英文
            "混合text文字"  # 中英混合
        ]

        for text in test_texts:
            ascii_result = renderer._to_ascii_text(text).decode('ascii')
            print(f"  '{text}' -> '{ascii_result}'")

        print("\n3. 测试文字绘制方法:")
        if renderer.display:
            print("  ✅ 显示环境可用，可以进行实际绘制测试")

            try:
                from Xlib import X

                # 创建测试窗口
                test_window = renderer.root.create_window(
                    100, 100, 200, 100, 0,
                    renderer.screen.root_depth,
                    X.InputOutput, X.CopyFromParent,
                    background_pixel=renderer.screen.black_pixel,
                    override_redirect=1
                )

                test_window.map()
                renderer.display.sync()

                # 测试不同文字的绘制
                test_texts_draw = ["按钮", "Button", "文本:测试", "Text:Test"]

                for i, text in enumerate(test_texts_draw):
                    print(f"    测试绘制: '{text}'")
                    success = renderer._try_draw_text(test_window, text, 10, 20 + i * 20)
                    print(f"    结果: {'成功' if success else '失败'}")

                time.sleep(2)  # 显示2秒
                test_window.destroy()

            except Exception as draw_e:
                print(f"    绘制测试异常: {draw_e}")

        else:
            print("  ⚠️ 无显示环境，跳过实际绘制测试")

        print("\n✅ 文字渲染功能测试完成")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_settings():
    """测试颜色设置"""
    print("\n🎨 测试颜色设置")
    print("=" * 30)

    try:
        from widget_capture_module import HighlightRenderer

        renderer = HighlightRenderer(debug=True)

        if renderer.display:
            print("✅ 显示环境可用")

            # 检查颜色设置
            if renderer.text_gc:
                print("✅ 文字图形上下文创建成功")
                print("  - 前景色: 白色")
                print("  - 背景色: 黑色")
            else:
                print("❌ 文字图形上下文创建失败")

            # 检查边框颜色
            if renderer.border_color:
                print("✅ 边框颜色设置成功")
                print("  - 边框色: 红色")
            else:
                print("⚠️ 边框颜色设置可能有问题")

        else:
            print("⚠️ 无显示环境，无法测试颜色设置")

        return True

    except Exception as e:
        print(f"❌ 颜色测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试文字渲染和颜色修复")

    # 检查环境
    display = os.environ.get('DISPLAY')
    if display:
        print(f"✅ 检测到显示环境: {display}")
    else:
        print("⚠️ 未检测到显示环境，部分测试将被跳过")

    # 运行测试
    test_text_rendering()
    test_color_settings()

    print("\n✨ 所有测试完成")
    print("\n📝 修复总结:")
    print("1. ✅ 改进了文字颜色设置，使用正确的颜色分配方法")
    print("2. ✅ 添加了中文字体支持，尝试使用Unicode字体")
    print("3. ✅ 实现了中英文自动切换，中文失败时使用英文")
    print("4. ✅ 优化了文字绘制方法，使用多种备用方案")
    print("5. ✅ 添加了ASCII转换功能，确保最基本的显示")
